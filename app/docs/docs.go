// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "consumes": [
        "application/json"
    ],
    "produces": [
        "application/json"
    ],
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "license": {
            "name": "MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/accounts/{account_id}/admin-role": {
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "This endpoint grants the admin role to a site member\n\nAn admin can:\n- Create an ISN\n- Define the signal_types used in the ISN\n- read/write to their own ISNs\n- Grant other accounts read or write access to their ISNs\n- Create service accounts\n\nthis endpoint can only be used by the site owner account",
                "tags": [
                    "Site admin"
                ],
                "summary": "Grant admin role",
                "parameters": [
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "account id",
                        "name": "account_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "this endpoint can only be used by the site owner account",
                "tags": [
                    "Site admin"
                ],
                "summary": "Revoke admin role",
                "parameters": [
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "account id",
                        "name": "account_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/accounts/{account_id}/disable": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "**Use Cases:**\n- **Security Incident**: Compromised account needs immediate lockout\n- **Employee Departure**: Remove access for departed staff\n\n**Actions Performed:**\n- Sets ` + "`" + `is_active = false` + "`" + ` (account becomes unusable)\n- Revokes all client secrets/one-time secrets (service accounts)\n- Revokes all refresh tokens (web users)\n\n**Recovery:** Account must be re-enabled by admin via ` + "`" + `/admin/accounts/{id}/enable` + "`" + `\nService accounts also need re-registration via ` + "`" + `/api/auth/register/service-accounts` + "`" + `\n\n**Note:** The site owner account cannot be disabled to prevent system lockout.\nOnly owners and admins can disable accounts.",
                "tags": [
                    "Site admin"
                ],
                "summary": "Disable an account",
                "parameters": [
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "Account ID to disable",
                        "name": "account_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Invalid account ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Cannot disable site owner account",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Account not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/accounts/{account_id}/enable": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "**Administrative endpoint to re-enable previously disabled accounts.**\nSets account status to ` + "`" + `is_active = true` + "`" + ` (does not create new tokens).\n\n**Post-Enable Steps Required:**\n- **Service Accounts**: Must re-register via ` + "`" + `/api/auth/register/service-accounts` + "`" + ` (same client_id, new credentials)\n- **Web Users**: Can immediately log in again via ` + "`" + `/auth/login` + "`" + `\n\nOnly owners and admins can enable accounts.",
                "tags": [
                    "Site admin"
                ],
                "summary": "Enable an account",
                "parameters": [
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "Account ID to enable",
                        "name": "account_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Invalid account ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Account not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/reset": {
            "post": {
                "description": "Delete all registered users and associated data.\nThis endpoint only works on environments configured as 'dev'",
                "tags": [
                    "Site admin"
                ],
                "summary": "reset",
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/service-accounts": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Get a list of all service accounts in the system.\nOnly owners and admins can view service account lists.",
                "tags": [
                    "Site admin"
                ],
                "summary": "Get all service accounts",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.ServiceAccountDetails"
                            }
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/service-accounts/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Get a specific service account by account ID.\nOnly owners and admins can view service account details.",
                "tags": [
                    "Site admin"
                ],
                "summary": "Get service account",
                "parameters": [
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "Service Account ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ServiceAccountDetails"
                        }
                    },
                    "400": {
                        "description": "Invalid service account ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Service account not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "This api displays all the site users and their email addreses (can only be used by owner account)",
                "tags": [
                    "Site admin"
                ],
                "summary": "Get registered users",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.UserDetails"
                            }
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "This api displays a site user and their email addreses (can only be used by owner account)",
                "tags": [
                    "Site admin"
                ],
                "summary": "Get registered user",
                "parameters": [
                    {
                        "type": "string",
                        "example": "68fb5f5b-e3f5-4a96-8d35-cd2203a06f73",
                        "description": "user id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.UserDetails"
                        }
                    },
                    "400": {
                        "description": "Invalid user ID format",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Insufficient permissions ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/{user_id}/reset-password": {
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Allows admins to reset a user's password (use this endpoint if the user has forgotten their password)",
                "tags": [
                    "Site admin"
                ],
                "summary": "Reset user password",
                "parameters": [
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "User Account ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "New password",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.ResetUserPasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ResetUserPasswordResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - admin role required",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/login": {
            "post": {
                "description": "The response body includes an access token which can be used to access the protected enpoints, assuming the account has the appropriate permissions.\nThe access_token is valid for 30 minutes.\n\nAs part of the login response, the server sets a http-only cookie on the client that will allow it to refresh the token (use the /oauth/token endpoint with a grant_type=refresh_token param)\nThe refresh_token lasts 30 days unless it is revoked earlier.\n- To renew the refresh_token, log in again.\n- To revoke the refresh_token, call the /oauth/revoke endpoint.\n\nThe account's role and permissions are encoded as part of the jwt access token and this information is also provided in the response body.",
                "tags": [
                    "auth"
                ],
                "summary": "Login",
                "parameters": [
                    {
                        "description": "email and password",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/auth.AccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/password/reset": {
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Self-service endpoint for users to reset their password.  Requires a valid access token and the current password\n",
                "tags": [
                    "auth"
                ],
                "summary": "Password reset",
                "parameters": [
                    {
                        "description": "user details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.UpdatePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad request with possible error codes: malformed_body, password_too_short",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized with possible error code: authentication_error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/register": {
            "post": {
                "description": "The first user created is granted the \"owner\" role and has super-user access to the site.\n\nWeb users can register directly and default to standard member roles.\nNew members can't access any information beyond the public data on the site until an admin grants them access to an ISN.\n\nThe site owner can grant other users the admin role.\nAdmins can create ISNs and service accounts and grant other accounts permissions to read or write to ISNs they created.",
                "tags": [
                    "auth"
                ],
                "summary": "Register user",
                "parameters": [
                    {
                        "description": "user details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    },
                    "400": {
                        "description": "Bad request with possible error codes: malformed_body, password_too_short",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict with possible error code: resource_already_exists",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/register/service-accounts": {
            "post": {
                "security": [
                    {
                        "BearerServiceAccount": []
                    }
                ],
                "description": "Registring a new service account creates a one time link with the client credentials in it - this must be used by the client within 48 hrs.\n\nIf you want to reissue a client's credentials call this endpoint again with the same client organization and contact email.\nA new one time setup url will be generated and the old one will be revoked.\nNote the client_id will remain the same and any existing client secrets will be revoked.\n\nYou have to be an admin or the site owner to use this endpoint\n",
                "tags": [
                    "Service accounts"
                ],
                "summary": "Register a new service account",
                "parameters": [
                    {
                        "description": "service account details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateServiceAccountRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateServiceAccountResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/service-accounts/rotate-secret": {
            "post": {
                "description": "Self-service endpoint for service accounts to rotate their client secret.\nThis endpoint requires current valid client_id and client_secret for authentication.\nThe old secret remains valid for 5 minutes to prevent race conditions when multiple instances are involved and to stop clients being locked out where network issues prevent them from receiving the new secret immediately.\n\n**Use Cases:**\n- Regular credential rotation for security compliance\n- Suspected credential compromise requiring immediate rotation\n",
                "tags": [
                    "auth"
                ],
                "summary": "Rotate service account client secret",
                "parameters": [
                    {
                        "description": "Service account credentials",
                        "name": "request",
                        "in": "body",
                        "schema": {
                            "$ref": "#/definitions/handlers.ServiceAccountTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.ServiceAccountRotateResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/service-accounts/setup/{setup_id}": {
            "get": {
                "description": "Exchange one-time setup token for permanent client credentials (the one-time request url is created when a new service account is registered).\nthe endpoint renders a html page that the user can use to copy their client credentials.\nThe setup url is only valid for 48 hours.\n",
                "tags": [
                    "Service accounts"
                ],
                "summary": "Complete service account setup",
                "parameters": [
                    {
                        "type": "string",
                        "example": "550e8400-e29b-41d4-a716-************",
                        "description": "One-time setup ID",
                        "name": "setup_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "410": {
                        "description": "Gone",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn": {
            "get": {
                "description": "get a list of the configured ISNs",
                "tags": [
                    "ISN details"
                ],
                "summary": "Get the ISNs",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.Isn"
                            }
                        }
                    }
                }
            }
        },
        "/api/isn/": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    },
                    {
                        "RefreshTokenCookieAuth": []
                    }
                ],
                "description": "Create an Information Sharing Network (ISN)\n\nvisibility = \"private\" means that signalsd on the network can only be seen by network participants.\n\nISN admins automatically get write permission for their own ISNs.\nSite owners automatically get write permission on all ISNs.\n\nThis endpoint can only be used by the site owner or an admin\n\nNote there is a cache of public ISNs that is used by the search endpoints. This cache is not dynamically loaded, so adding public ISNs requires a restart of the service",
                "tags": [
                    "ISN configuration"
                ],
                "summary": "Create an ISN",
                "parameters": [
                    {
                        "description": "ISN details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateIsnRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateIsnResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}": {
            "get": {
                "description": "Returns details about the ISN",
                "tags": [
                    "ISN details"
                ],
                "summary": "Get an ISN configuration",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "isn slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.IsnAndLinkedInfo"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Update the ISN details\nThis endpoint can only be used by the site owner or the ISN admin",
                "tags": [
                    "ISN configuration"
                ],
                "summary": "Update an ISN",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "isn slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "ISN details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.UpdateIsnRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/accounts": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Get a list of all accounts (users and service accounts) that have permissions on the specified ISN.\nOnly ISN admins and site owners can view this information.",
                "tags": [
                    "ISN details"
                ],
                "summary": "Get all accounts with access to an ISN",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.IsnAccount"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/accounts/{account_id}": {
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Grant an account read or write access to an isn.\nThis end point can only be used by the site owner or the isn admin account.",
                "tags": [
                    "ISN Permissions"
                ],
                "summary": "Grant ISN access permission",
                "parameters": [
                    {
                        "description": "permission details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.GrantIsnAccountPermissionRequest"
                        }
                    },
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "isn slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "account id",
                        "name": "account_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/batches": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "This endpoint is used by service accounts to create a new batch. Batches are used to track signals sent by an account to the specified ISN.\n\nOpening a batch closes the previous batch (the client app can decide how long to keep a batch open)\n\nSignals can only be sent to open batches.\n\nAuthentication is based on the supplied access token:\nthe site owner, the isn admin and members with an isn_perm=write can create a batch for the ISN.\n\nNote this endpoint is not needed for web users (a batch is automatically created when they first write to an isn and is only closed if their permission to write to the ISN is revoked)\n",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Create a new signal batch",
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalsBatchResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/signal_types": {
            "get": {
                "description": "Get details for the signal types defined on the ISN",
                "tags": [
                    "Signal types"
                ],
                "summary": "Get Signal types",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "sample-signal--example-org",
                        "description": "signal type slug",
                        "name": "signal_type_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0.0.1",
                        "description": "version to be deleted",
                        "name": "sem_ver",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.SignalTypeDetail"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Signal types specify a record that can be shared over the ISN\n- Each type has a unique title and this is used to create a URL-friendly slug\n- The title and slug fields can't be changed and it is not allowed to reuse a slug that was created by another account.\n- The signal type fields are defined in an external JSON schema file and this schema file is used to validate signals before loading\n\nSchema URL Requirements\n- Must be a liink to a schema file on a public github repo (e.g., https://github.com/org/repo/blob/2025.01.01/schema.json)\n- To disable schema validation, use the special URL: https://github.com/skip/validation/main/schema.json\n\nVersions\n- A signal type can have multiple versions - these share the same title/slug but have different JSON schemas\n- Use this endpoint to create the first version - the bump_type (major/minor/patch) determines the initial semver (1.0.0, 0.1.0 or 0.0.1)\n- Subsequent POSTs to this endpoint that reference a previously submitted title/slug but point to a different schema will increment the version based on the supplied bump_type\n\nSignal type definitions are referred to like this: /api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver} (e.g., /api/isn/sample-isn--example-org/signal_types/sample-signal--example-org/v0.0.1)\n",
                "tags": [
                    "Signal types"
                ],
                "summary": "Create signal type",
                "parameters": [
                    {
                        "description": "signal type details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalTypeRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalTypeResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}": {
            "get": {
                "description": "Returns details about the signal type",
                "tags": [
                    "Signal types",
                    "ISN details"
                ],
                "summary": "Get signal type",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "sample-signal--example-org",
                        "description": "signal type slug",
                        "name": "signal_type_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0.0.1",
                        "description": "version to be recieved",
                        "name": "sem_ver",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.SignalTypeDetail"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "users can mark the signal type as *in use/not in use* and update the description or link to the readme file\nSignal types marked as 'not in use' are not returned in signal searches and can not receive new signals",
                "tags": [
                    "Signal types"
                ],
                "summary": "Update signal type",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "sample-signal--example-org",
                        "description": "signal type slug",
                        "name": "signal_type_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0.0.1",
                        "description": "Sem ver",
                        "name": "sem_ver",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "signal type details to be updated",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.UpdateSignalTypeRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Only signal types that have never been referenced by signals can be deleted",
                "tags": [
                    "Signal types"
                ],
                "summary": "Delete signal type",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "sample-signal--example-org",
                        "description": "signal type slug",
                        "name": "signal_type_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0.0.1",
                        "description": "version to be deleted",
                        "name": "sem_ver",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Submit an array of signals for storage on the ISN\n- payloads must not mix signals of different types and are subject to the size limits defined on the site.\n- The client-supplied local_ref must uniquely identify each signal of the specified signal type that will be supplied by the account.\n- If a local reference is received more than once from an account for the specified signal_type a new version of the signal will be stored with a incremented version number.\n- Optionally a correlation_id can be supplied - this will link the signal to a previously received signal. The correlated signal does not need to be owned by the same account but must be in the same ISN.\n- requests are only accepted for the open signal batch for this account/ISN (service accounts need to manually create batches, web users have a batch automatically created when they first write to an ISN).\n\n**Authentication**\n\nRequires a valid access token.\nThe claims in the access token list the ISNs and signal_types that the account is permitted to use.\n\n**Error handling**\n\nif the request is a vaild format but individual signals contain errors (validation errors, incorrect correlation ids, database errors) the errors are recorded in the response but do not prevent other signals from being processed.\nIndividual failures are logged and can be tracked using the signals_batch_id returned in the response - see the batch status endpoint.\n\nErrors that relate to the entire request  - e.g invalid json, authentication, permission and server errors (400, 401, 403, 500) - are not recorded and should be handled by the client immediately.\n\n**JSON Schema Validation**\n\nSignals are validated against the JSON schema specified for the signal type unless validation is disabled on the type definition.\n\nWhen validation is disabled, basic checks are still done on the incoming data and the following issues create a 400 error and cause the entire payload to be rejected:\n- invalid json format\n- missing fields (the array of signals must be in a json object called signals, and content and local_ref must be present for each record).\n\n**Signal versions**\n\nMultiple versions are created when signals are resupplied using the same local_ref, e.g. because the client wants to correct a previously publsihed signal.\nBy default search will return the latest version of the signal.\nIf a signal has been withdrawn it will be reactivated if you resubmit it using the same local_ref.\n\n**Correlating signals**\n\nCorrelation IDs can be used to link signals together.  Signals can only be correlated within the same ISN.\nIf the supplied correlation_id is not found in the same ISN as the signal being submitted, the response will contain a 422 or 207 status code and the error_code for the failed signal will be ` + "`" + `invalid_correlation_id` + "`" + `.\n\nrequest level errors (e.g. invalid json, authentication failure etc) return a simple error_code/error_message response rather than a detailed audit log",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Create signals",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "isn slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "sample-signal--example-org",
                        "description": "signal type slug",
                        "name": "signal_type_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0.0.1",
                        "description": "signal type sem_ver number",
                        "name": "sem_ver",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "create signals",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "All signals processed successfully",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalsResponse"
                        }
                    },
                    "207": {
                        "description": "Partial success - some signals succeeded, some failed",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalsResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format (error_code = malformed_body)",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized request (invalid credentials, error_code = authentication_error)",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden (no permission to write to ISN, error_code = forbidden)",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found (mistyped url or signal_type marked 'not in use')",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "422": {
                        "description": "Valid request format but all signals failed processing - returns detailed error information",
                        "schema": {
                            "$ref": "#/definitions/handlers.CreateSignalsResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals/search": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Search for signals by date or account in private ISNs (authentication required - only accounts with read or write permissions to the ISN can access signals).\n\nNote the endpoint returns the latest version of each signal.",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Signal Search (private ISNs)",
                "parameters": [
                    {
                        "type": "string",
                        "example": "2006-01-02T15:05:00Z",
                        "description": "Start date",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2006-01-02T15:15:00Z",
                        "description": "End date",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "def87f89-dab6-4607-95f7-593d61cb5742",
                        "description": "Account ID",
                        "name": "account_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "4cedf4fa-2a01-4cbf-8668-6b44f8ac6e19",
                        "description": "Signal ID",
                        "name": "signal_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "item_id_#1",
                        "description": "Local reference",
                        "name": "local_ref",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "description": "Include withdrawn signals (default: false)",
                        "name": "include_withdrawn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "description": "Include signals that link to each returned signal (default: false)",
                        "name": "include_correlated",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "description": "Include previous versions of each returned signal (default: false)",
                        "name": "include_previous_versions",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.SearchSignalResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals/withdraw": {
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Withdraw a signal by local reference\n\nWithdrawn signals are hidden from search results by default but remain in the database.\nSignals can only be withdrawn by the account that created the signal.\nTo reactivate a signal resupply it with the same local_ref using the 'create signals' end point.",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Withdraw a signal",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "signal-type-1",
                        "description": "Signal type slug",
                        "name": "signal_type_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "0.0.1",
                        "description": "Signal type version",
                        "name": "sem_ver",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Withdrawal request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.WithdrawSignalRequest"
                        }
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/isn/{isn_slug}/transfer-ownership": {
            "put": {
                "security": [
                    {
                        "BearerAccessToken": []
                    },
                    {
                        "RefreshTokenCookieAuth": []
                    }
                ],
                "description": "Transfer ownership of an ISN to another admin account.\nThis can be used when an admin leaves or when reorganizing responsibilities.\nOnly the site owner can transfer ISN ownership.",
                "tags": [
                    "ISN configuration"
                ],
                "summary": "Transfer ISN ownership",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Transfer details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/handlers.TransferIsnOwnershipRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/public/isn/{isn_slug}/signal_types/{signal_type_slug}/v{sem_ver}/signals/search": {
            "get": {
                "description": "Search for signals in public ISNs (no authentication required).\n\nNote the endpoint returns the latest version of each signal.",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Signal Search (public ISNs)",
                "parameters": [
                    {
                        "type": "string",
                        "example": "2006-01-02T15:05:00Z",
                        "description": "Start date",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2006-01-02T15:15:00Z",
                        "description": "End date",
                        "name": "end_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "def87f89-dab6-4607-95f7-593d61cb5742",
                        "description": "Account ID",
                        "name": "account_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "4cedf4fa-2a01-4cbf-8668-6b44f8ac6e19",
                        "description": "Signal ID",
                        "name": "signal_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "item_id_#1",
                        "description": "Local reference",
                        "name": "local_ref",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "description": "Include withdrawn signals (default: false)",
                        "name": "include_withdrawn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "description": "Include signals that link to each returned signal (default: false)",
                        "name": "include_correlated",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "true",
                        "description": "Include previous versions of each returned signal (default: false)",
                        "name": "include_previous_versions",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.SearchSignalResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/webhooks": {
            "post": {
                "description": "register a webhook to recieve signals batch status updates",
                "tags": [
                    "Service accounts"
                ],
                "summary": "Register webhook (TODO)",
                "responses": {
                    "204": {
                        "description": "Not implemented",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/health/live": {
            "get": {
                "description": "Check if the signalsd http service is alive and responding.",
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Liveness Check",
                "responses": {
                    "200": {
                        "description": "OK - Service is alive",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/health/ready": {
            "get": {
                "description": "Check if the signalsd service is ready to accept traffic.",
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Readiness Check",
                "responses": {
                    "200": {
                        "description": "OK - Service is ready",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "503": {
                        "description": "Service Unavailable - Database connection failed",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/isn/{isn_slug}/accounts/{account_id}": {
            "delete": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Revoke an account read or write access to an isn.\nThis end point can only be used by the site owner or the isn admin account.",
                "tags": [
                    "ISN Permissions"
                ],
                "summary": "Revoke ISN access permission",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "isn slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3",
                        "description": "account id",
                        "name": "account_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "No Content"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/isn/{isn_slug}/batches/search": {
            "get": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Search for batches with optional filtering parameters\n\nThe search endpoint returns the full batch status for each batch.\n\nWhere a signal has failed to load as part of the batch and not subsequently been loaded, the failure is considered unresolved and listed as a failure in the batch status\n\nMember accounts can only see batches they have created. ISN Admins can see batches for ISNs they administer. The site owner can see all batches.\n\nAt least one search criteria must be provided:\n- latest=true (get the latest batch)\n- previous=true (get the previous batch)\n- created date range\n- closed date range\n",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Search for batches",
                "parameters": [
                    {
                        "type": "boolean",
                        "example": true,
                        "description": "Get the latest batch",
                        "name": "latest",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "example": true,
                        "description": "Get the previous batch",
                        "name": "previous",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2006-01-02T15:04:05Z",
                        "description": "Start date for batch creation filtering",
                        "name": "created_after",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2006-01-02T16:00:00Z",
                        "description": "End date for batch creation filtering",
                        "name": "created_before",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2006-01-02T15:04:05Z",
                        "description": "Start date for batch closure filtering",
                        "name": "closed_after",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "2006-01-02T16:00:00Z",
                        "description": "End date for batch closure filtering",
                        "name": "closed_before",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/handlers.BatchStatusResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/isn/{isn_slug}/batches/{batch_id}/status": {
            "get": {
                "description": "Returns the status of a batch, including the number of signals loaded and the number of failures for each signal type\n\nThe endpoint returns the full batch status for the batch\n\nWhere a signal has failed to load as part of the batch and not subsequently been loaded, the failure is considered unresolved and listed as a failure in the batch status\n\nNote:  Unresolved failures are signals that failed to load in this batch and have not been successfully loaded since the failure occurred.\nIf a signal is fixed but subsequently fails again in a later batch, it will be recorded as a new failure, and this new failure will appear in that batch's status.\n\nMember accounts can see the status of batches that they created.\nISN Admins can see the status of any batch created for ISNs they administer.\nThe site owner can see the status of any batch on the site.\n",
                "tags": [
                    "Signal sharing"
                ],
                "summary": "Get batch processing status",
                "parameters": [
                    {
                        "type": "string",
                        "example": "sample-isn--example-org",
                        "description": "ISN slug",
                        "name": "isn_slug",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "example": "********-3b14-42cf-b785-df28ce570400",
                        "description": "Batch ID",
                        "name": "batch_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.BatchStatusResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/oauth/revoke": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "Revoke a refresh token or client secret to prevent it being used to create new access tokens (self-service)\n\n**Use Cases:**\n- **Web User Logout**: User wants to log out of their session\n- **Service Account Security**: Account no longer being used/compromised secret\n\n**Service Accounts:**\nYou must supply your ` + "`" + `client ID` + "`" + ` and ` + "`" + `client secret` + "`" + ` in the request body.\nThis revokes all client secrets for the service account, effectively disabling it.\n\n**IMPORTANT - Service Account Reinstatement:**\n- This endpoint does not permanently disable the service account itself (use ` + "`" + `POST /admin/accounts/{account_id}/disable` + "`" + ` for that)\n- To restore access, an admin must call ` + "`" + `POST /api/auth/register/service-accounts` + "`" + ` with the same organization and email\n- This will generate a new setup URL and client secret while preserving the same client_id\n- If the account was disabled by an admin, it must first be re-enabled via ` + "`" + `POST /admin/accounts/{account_id}/enable` + "`" + `\n\n**Web Users (Logout):**\nThis endpoint expects a refresh token in an ` + "`" + `http-only cookie` + "`" + ` and a valid access token in the Authorization header.\nThis revokes the user's refresh token, effectively logging them out.\n\nIf the refresh token has expired or been revoked, the user must login again to get a new one.\n\nYou must also provide a previously issued ` + "`" + `bearer access token` + "`" + ` in the Authorization header - it does not matter if it has expired\n(the token is not used to authenticate the request but is needed to establish the ID of the user making the request).\n\n**Note:** Any unexpired access tokens issued for this user will continue to work until they expire.\nUsers must log in again to obtain a new refresh token after logout/revocation.\n\n**Client Examples:**\n- **Web User Logout:** ` + "`" + `POST /oauth/revoke` + "`" + ` with refresh token cookie + Authorization header\n- **Service Account:** ` + "`" + `POST /oauth/revoke` + "`" + ` with client_id and client_secret in request body\n",
                "tags": [
                    "auth"
                ],
                "summary": "Revoke a token",
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Invalid request body ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Token not found or already revoked",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/oauth/token": {
            "post": {
                "security": [
                    {
                        "BearerAccessToken": []
                    }
                ],
                "description": "**Client Credentials Grant (Service Accounts):**\n\nIssues new access token (in response body)\n\n- Set ` + "`" + `grant_type=client_credentials` + "`" + ` as URL parameter\n- Provide ` + "`" + `client_id` + "`" + ` and ` + "`" + `client_secret` + "`" + ` in request body\n- Access tokens expire after 30 minutes\n(subsequent requests using the token will fail with HTTP status 401 and an error_code of \"access_token_expired\")\n\n**Refresh Token Grant (Web Users):**\n\nIssues new access token (in response body) and rotates refresh token (HTTP-only cookie)\n\n- Set ` + "`" + `grant_type=refresh_token` + "`" + ` as URL parameter\n- Must provide current access token in Authorization header (expired tokens accepted)\n- Must have valid refresh token cookie\n- Access tokens expire after 30 minutes\n(subsequent requests using the token will fail with HTTP status 401 and an error_code of \"access_token_expired\")\n- Refresh tokens expire after 30 days\n- subsequent requests using the refresh token will fail with HTTP status 401 and an error_code of \"refresh_token_expired\" and users must login again to get a new one.\n",
                "tags": [
                    "auth"
                ],
                "summary": "New Access Token",
                "parameters": [
                    {
                        "enum": [
                            "client_credentials",
                            "refresh_token"
                        ],
                        "type": "string",
                        "description": "grant type",
                        "name": "grant_type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "Service account credentials (required for client_credentials grant)",
                        "name": "request",
                        "in": "body",
                        "schema": {
                            "$ref": "#/definitions/handlers.ServiceAccountTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/auth.AccessTokenResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid grant_type parameter ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication failed ",
                        "schema": {
                            "$ref": "#/definitions/responses.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/version": {
            "get": {
                "description": "Returns the current API version details",
                "tags": [
                    "Site admin"
                ],
                "summary": "Get API version",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/version.Info"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "apperrors.ErrorCode": {
            "type": "string",
            "enum": [
                "access_token_expired",
                "all_signals_failed_processing",
                "authentication_error",
                "authorization_error",
                "database_error",
                "forbidden",
                "invalid_correlation_id",
                "internal_error",
                "invalid_request",
                "invalid_url_param",
                "malformed_body",
                "not_implemented",
                "password_too_short",
                "refresh_token_invalid",
                "request_too_large",
                "rate_limit_exceeded",
                "resource_already_exists",
                "resource_expired",
                "resource_in_use",
                "resource_not_found",
                "token_invalid"
            ],
            "x-enum-varnames": [
                "ErrCodeAccessTokenExpired",
                "ErrCodeAllSignalsFailedProcessing",
                "ErrCodeAuthenticationFailure",
                "ErrCodeAuthorizationFailure",
                "ErrCodeDatabaseError",
                "ErrCodeForbidden",
                "ErrCodeInvalidCorrelationID",
                "ErrCodeInternalError",
                "ErrCodeInvalidRequest",
                "ErrCodeInvalidURLParam",
                "ErrCodeMalformedBody",
                "ErrCodeNotImplemented",
                "ErrCodePasswordTooShort",
                "ErrCodeRefreshTokenInvalid",
                "ErrCodeRequestTooLarge",
                "ErrCodeRateLimitExceeded",
                "ErrCodeResourceAlreadyExists",
                "ErrCodeResourceExpired",
                "ErrCodeResourceInUse",
                "ErrCodeResourceNotFound",
                "ErrCodeTokenInvalid"
            ]
        },
        "auth.AccessTokenResponse": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.33ANor7XHWkB87npB4RWsJUjBnJHdYZce-lT8w_IN_s"
                },
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "account_type": {
                    "type": "string",
                    "enum": [
                        "user",
                        "service_account"
                    ]
                },
                "expires_in": {
                    "description": "seconds",
                    "type": "integer",
                    "example": 1800
                },
                "isn_perms": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/auth.IsnPerms"
                    }
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "owner",
                        "admin",
                        "member"
                    ],
                    "example": "admin"
                },
                "token_type": {
                    "type": "string",
                    "example": "Bearer"
                }
            }
        },
        "auth.IsnPerms": {
            "type": "object",
            "properties": {
                "permission": {
                    "type": "string",
                    "enum": [
                        "read",
                        "write"
                    ],
                    "example": "read"
                },
                "signal_batch_id": {
                    "type": "string",
                    "example": "967affe9-5628-4fdd-921f-020051344a12"
                },
                "signal_types": {
                    "description": "list of available signal types for the isn",
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "signal-type-1/v0.0.1",
                        "signal-type-2/v1.0.0"
                    ]
                }
            }
        },
        "handlers.BatchStatus": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "type": "integer"
                },
                "signal_type_slug": {
                    "type": "string"
                },
                "signal_type_version": {
                    "type": "string"
                },
                "stored_count": {
                    "type": "integer"
                },
                "unresolved_failures": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.FailureRow"
                    }
                }
            }
        },
        "handlers.BatchStatusResponse": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "batch_id": {
                    "type": "string"
                },
                "batch_status": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.BatchStatus"
                    }
                },
                "closed_at": {
                    "type": "string"
                },
                "contains_failures": {
                    "type": "boolean"
                },
                "created_at": {
                    "type": "string"
                },
                "is_latest": {
                    "type": "boolean"
                },
                "isn_slug": {
                    "type": "string"
                }
            }
        },
        "handlers.CreateIsnRequest": {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "string",
                    "example": "Sample ISN description"
                },
                "is_in_use": {
                    "type": "boolean",
                    "example": true
                },
                "title": {
                    "type": "string",
                    "example": "Sample ISN @example.org"
                },
                "visibility": {
                    "type": "string",
                    "enum": [
                        "public",
                        "private"
                    ],
                    "example": "private"
                }
            }
        },
        "handlers.CreateIsnResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "example": "********-3b14-42cf-b785-df28ce570400"
                },
                "resource_url": {
                    "type": "string",
                    "example": "http://localhost:8080/api/isn/sample-isn--example-org"
                },
                "slug": {
                    "type": "string",
                    "example": "sample-isn--example-org"
                }
            }
        },
        "handlers.CreateServiceAccountRequest": {
            "type": "object",
            "properties": {
                "client_contact_email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "client_organization": {
                    "type": "string",
                    "example": "example org"
                }
            }
        },
        "handlers.CreateServiceAccountResponse": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-************"
                },
                "client_id": {
                    "type": "string",
                    "example": "sa_example-org_k7j2m9x1"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2024-12-25T10:30:00Z"
                },
                "expires_in": {
                    "type": "integer",
                    "example": 172800
                },
                "setup_url": {
                    "type": "string",
                    "example": "https://api.example.com/api/auth/service-accounts/setup/550e8400-e29b-41d4-a716-************"
                }
            }
        },
        "handlers.CreateSignal": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "object"
                },
                "correlation_id": {
                    "description": "optional - supply the id of another signal if you want to link to it",
                    "type": "string",
                    "example": "75b45fe1-ecc2-4629-946b-fd9058c3b2ca"
                },
                "local_ref": {
                    "type": "string",
                    "example": "item_id_#1"
                }
            }
        },
        "handlers.CreateSignalTypeRequest": {
            "type": "object",
            "properties": {
                "bump_type": {
                    "description": "this is used to increment semver for the signal type",
                    "type": "string",
                    "enum": [
                        "major",
                        "minor",
                        "patch"
                    ],
                    "example": "patch"
                },
                "detail": {
                    "description": "description",
                    "type": "string",
                    "example": "description"
                },
                "readme_url": {
                    "description": "README file URL: must be a GitHub URL ending in .md",
                    "type": "string",
                    "example": "https://github.com/user/project/blob/2025.01.01/readme.md"
                },
                "schema_url": {
                    "description": "JSON schema URL: must be a GitHub URL ending in .json, OR use https://github.com/skip/validation/main/schema.json to disable validation",
                    "type": "string",
                    "example": "https://github.com/user/project/blob/2025.01.01/schema.json"
                },
                "title": {
                    "description": "unique title",
                    "type": "string",
                    "example": "Sample Signal @example.org"
                }
            }
        },
        "handlers.CreateSignalTypeResponse": {
            "type": "object",
            "properties": {
                "resource_url": {
                    "type": "string",
                    "example": "http://localhost:8080/api/isn/sample-isn--example-org/signals_types/sample-signal--example-org/v0.0.1"
                },
                "sem_ver": {
                    "type": "string",
                    "example": "0.0.1"
                },
                "slug": {
                    "type": "string",
                    "example": "sample-signal--example-org"
                }
            }
        },
        "handlers.CreateSignalsBatchResponse": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "resource_url": {
                    "type": "string",
                    "example": "http://localhost:8080/api/isn/sample-isn--example-org/account/{account_id}/batch/{signals_batch_id}"
                },
                "signals_batch_id": {
                    "type": "string",
                    "example": "b51faf05-aaed-4250-b334-2258ccdf1ff2"
                }
            }
        },
        "handlers.CreateSignalsRequest": {
            "type": "object",
            "properties": {
                "signals": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.CreateSignal"
                    }
                }
            }
        },
        "handlers.CreateSignalsResponse": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "isn_slug": {
                    "type": "string",
                    "example": "sample-isn--example-org"
                },
                "results": {
                    "$ref": "#/definitions/handlers.CreateSignalsResults"
                },
                "signal_type_path": {
                    "type": "string",
                    "example": "signal-type-1/v0.0.1"
                },
                "signals_batch_id": {
                    "type": "string",
                    "example": "b51faf05-aaed-4250-b334-2258ccdf1ff2"
                },
                "summary": {
                    "$ref": "#/definitions/handlers.CreateSignalsSummary"
                }
            }
        },
        "handlers.CreateSignalsResults": {
            "type": "object",
            "properties": {
                "failed_signals": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.FailedSignal"
                    }
                },
                "stored_signals": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.StoredSignal"
                    }
                }
            }
        },
        "handlers.CreateSignalsSummary": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "type": "integer",
                    "example": 5
                },
                "stored_count": {
                    "type": "integer",
                    "example": 95
                },
                "total_submitted": {
                    "type": "integer",
                    "example": 100
                }
            }
        },
        "handlers.CreateUserRequest": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "password": {
                    "description": "passwords must be at least 11 characters long",
                    "type": "string",
                    "example": "lkIB53@6O^Y"
                }
            }
        },
        "handlers.FailedSignal": {
            "type": "object",
            "properties": {
                "error_code": {
                    "type": "string",
                    "example": "validation_error"
                },
                "error_message": {
                    "type": "string",
                    "example": "field 'name' is required"
                },
                "local_ref": {
                    "type": "string",
                    "example": "item_id_#2"
                }
            }
        },
        "handlers.FailureRow": {
            "type": "object",
            "properties": {
                "error_code": {
                    "type": "string"
                },
                "error_message": {
                    "type": "string"
                },
                "local_ref": {
                    "type": "string"
                }
            }
        },
        "handlers.GrantIsnAccountPermissionRequest": {
            "type": "object",
            "properties": {
                "permission": {
                    "type": "string",
                    "example": "write"
                }
            }
        },
        "handlers.Isn": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "detail": {
                    "type": "string",
                    "example": "Sample ISN description"
                },
                "id": {
                    "type": "string",
                    "example": "********-3b14-42cf-b785-df28ce570400"
                },
                "is_in_use": {
                    "type": "boolean",
                    "example": true
                },
                "slug": {
                    "type": "string",
                    "example": "sample-isn--example-org"
                },
                "title": {
                    "type": "string",
                    "example": "Sample ISN @example.org"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "visibility": {
                    "type": "string",
                    "enum": [
                        "public",
                        "private"
                    ],
                    "example": "private"
                }
            }
        },
        "handlers.IsnAccount": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "account_role": {
                    "type": "string",
                    "enum": [
                        "owner",
                        "admin",
                        "member"
                    ],
                    "example": "admin"
                },
                "account_type": {
                    "type": "string",
                    "enum": [
                        "user",
                        "service_account"
                    ],
                    "example": "user"
                },
                "client_id": {
                    "type": "string",
                    "example": "client-123"
                },
                "client_organization": {
                    "type": "string",
                    "example": "Example Organization"
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "string",
                    "example": "********-3b14-42cf-b785-df28ce570400"
                },
                "is_active": {
                    "type": "boolean",
                    "example": true
                },
                "isn_id": {
                    "type": "string",
                    "example": "********-3b14-42cf-b785-df28ce570400"
                },
                "permission": {
                    "type": "string",
                    "enum": [
                        "read",
                        "write"
                    ],
                    "example": "write"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                }
            }
        },
        "handlers.IsnAndLinkedInfo": {
            "type": "object",
            "properties": {
                "isn": {
                    "$ref": "#/definitions/handlers.Isn"
                },
                "signal_types": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.SignalType"
                    }
                },
                "user": {
                    "$ref": "#/definitions/handlers.User"
                }
            }
        },
        "handlers.LoginRequest": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "password": {
                    "description": "passwords must be at least 11 characters long",
                    "type": "string",
                    "example": "lkIB53@6O^Y"
                }
            }
        },
        "handlers.PreviousSignalVersion": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "object"
                },
                "created_at": {
                    "type": "string"
                },
                "signal_version_id": {
                    "type": "string"
                },
                "version_number": {
                    "type": "integer"
                }
            }
        },
        "handlers.ResetUserPasswordRequest": {
            "type": "object",
            "properties": {
                "new_password": {
                    "description": "Admin provides the new password",
                    "type": "string"
                }
            }
        },
        "handlers.ResetUserPasswordResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                }
            }
        },
        "handlers.SearchSignal": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "account_type": {
                    "type": "string"
                },
                "content": {
                    "type": "object"
                },
                "correlated_to_signal_id": {
                    "type": "string"
                },
                "email": {
                    "description": "not included in public ISN searches",
                    "type": "string"
                },
                "is_withdrawn": {
                    "type": "boolean"
                },
                "local_ref": {
                    "type": "string"
                },
                "signal_created_at": {
                    "type": "string"
                },
                "signal_id": {
                    "type": "string"
                },
                "signal_version_id": {
                    "type": "string"
                },
                "version_created_at": {
                    "type": "string"
                },
                "version_number": {
                    "type": "integer"
                }
            }
        },
        "handlers.SearchSignalResponse": {
            "type": "object",
            "properties": {
                "signals": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.SearchSignalWithCorrelationsAndVersions"
                    }
                }
            }
        },
        "handlers.SearchSignalWithCorrelationsAndVersions": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "account_type": {
                    "type": "string"
                },
                "content": {
                    "type": "object"
                },
                "correlated_signals": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.SearchSignal"
                    }
                },
                "correlated_to_signal_id": {
                    "type": "string"
                },
                "email": {
                    "description": "not included in public ISN searches",
                    "type": "string"
                },
                "is_withdrawn": {
                    "type": "boolean"
                },
                "local_ref": {
                    "type": "string"
                },
                "previous_signal_versions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/handlers.PreviousSignalVersion"
                    }
                },
                "signal_created_at": {
                    "type": "string"
                },
                "signal_id": {
                    "type": "string"
                },
                "signal_version_id": {
                    "type": "string"
                },
                "version_created_at": {
                    "type": "string"
                },
                "version_number": {
                    "type": "integer"
                }
            }
        },
        "handlers.ServiceAccountDetails": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "client_contact_email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "client_id": {
                    "type": "string",
                    "example": "client-123"
                },
                "client_organization": {
                    "type": "string",
                    "example": "Example Organization"
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                }
            }
        },
        "handlers.ServiceAccountRotateResponse": {
            "type": "object",
            "properties": {
                "client_id": {
                    "type": "string",
                    "example": "sa_exampleorg_k7j2m9x1"
                },
                "client_secret": {
                    "type": "string",
                    "example": "dGhpcyBpcyBhIHNlY3JldA"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-07-05T10:30:00Z"
                },
                "expires_in": {
                    "description": "seconds (1 year)",
                    "type": "integer",
                    "example": ********
                }
            }
        },
        "handlers.ServiceAccountTokenRequest": {
            "type": "object",
            "properties": {
                "client_id": {
                    "type": "string",
                    "example": "sa_exampleorg_k7j2m9x1"
                },
                "client_secret": {
                    "type": "string",
                    "example": "dGhpcyBpcyBhIHNlY3JldA"
                }
            }
        },
        "handlers.SignalType": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "detail": {
                    "type": "string",
                    "example": "Sample signal type description"
                },
                "id": {
                    "type": "string",
                    "example": "********-3b14-42cf-b785-df28ce570400"
                },
                "is_in_use": {
                    "type": "boolean",
                    "example": true
                },
                "readme_url": {
                    "type": "string",
                    "example": "https://example.com/readme.md"
                },
                "schema_url": {
                    "type": "string",
                    "example": "https://example.com/schema.json"
                },
                "sem_ver": {
                    "type": "string",
                    "example": "1.0.0"
                },
                "slug": {
                    "type": "string",
                    "example": "sample-signal-type"
                },
                "title": {
                    "type": "string",
                    "example": "Sample Signal Type"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                }
            }
        },
        "handlers.SignalTypeDetail": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "detail": {
                    "type": "string",
                    "example": "Sample signal type description"
                },
                "id": {
                    "type": "string",
                    "example": "********-3b14-42cf-b785-df28ce570400"
                },
                "is_in_use": {
                    "type": "boolean",
                    "example": true
                },
                "readme_url": {
                    "type": "string",
                    "example": "https://github.com/user/project/blob/2025.01.01/readme.md"
                },
                "schema_url": {
                    "type": "string",
                    "example": "https://github.com/user/project/blob/2025.01.01/schema.json"
                },
                "sem_ver": {
                    "type": "string",
                    "example": "1.0.0"
                },
                "slug": {
                    "type": "string",
                    "example": "sample-signal-type"
                },
                "title": {
                    "type": "string",
                    "example": "Sample Signal Type"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                }
            }
        },
        "handlers.StoredSignal": {
            "type": "object",
            "properties": {
                "local_ref": {
                    "type": "string",
                    "example": "item_id_#1"
                },
                "signal_id": {
                    "type": "string",
                    "example": "b8ded113-ac0e-4a2c-a89f-0876fe97b440"
                },
                "signal_version_id": {
                    "type": "string",
                    "example": "835788bd-789d-4091-96e3-db0f51ccbabc"
                },
                "version_number": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "handlers.TransferIsnOwnershipRequest": {
            "type": "object",
            "properties": {
                "new_owner_account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                }
            }
        },
        "handlers.UpdateIsnRequest": {
            "type": "object",
            "properties": {
                "detail": {
                    "type": "string",
                    "example": "Sample ISN description"
                },
                "is_in_use": {
                    "type": "boolean",
                    "example": true
                },
                "visibility": {
                    "type": "string",
                    "enum": [
                        "public",
                        "private"
                    ],
                    "example": "private"
                }
            }
        },
        "handlers.UpdatePasswordRequest": {
            "type": "object",
            "properties": {
                "current_password": {
                    "type": "string",
                    "example": "lkIB53@6O^Y"
                },
                "new_password": {
                    "type": "string",
                    "example": "ue6U\u003e\u0026X3j570"
                }
            }
        },
        "handlers.UpdateSignalTypeRequest": {
            "type": "object",
            "properties": {
                "detail": {
                    "description": "updated description",
                    "type": "string",
                    "example": "description"
                },
                "is_in_use": {
                    "description": "whether this signal type version is actively used",
                    "type": "boolean",
                    "example": false
                },
                "readme_url": {
                    "description": "README file URL: must be a GitHub URL ending in .md",
                    "type": "string",
                    "example": "https://github.com/user/project/blob/2025.01.01/readme.md"
                }
            }
        },
        "handlers.User": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                }
            }
        },
        "handlers.UserDetails": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string",
                    "example": "a38c99ed-c75c-4a4a-a901-c9485cf93cf3"
                },
                "created_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2025-06-03T13:47:47.331787+01:00"
                },
                "user_role": {
                    "type": "string",
                    "enum": [
                        "owner",
                        "admin",
                        "member"
                    ],
                    "example": "admin"
                }
            }
        },
        "handlers.WithdrawSignalRequest": {
            "type": "object",
            "properties": {
                "local_ref": {
                    "type": "string",
                    "example": "item_id_#1"
                }
            }
        },
        "responses.ErrorResponse": {
            "type": "object",
            "properties": {
                "error_code": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/apperrors.ErrorCode"
                        }
                    ],
                    "example": "example_error_code"
                },
                "message": {
                    "type": "string",
                    "example": "message describing the error"
                }
            }
        },
        "version.Info": {
            "type": "object",
            "properties": {
                "build_date": {
                    "type": "string",
                    "example": "2025-01-01T12:00:00Z"
                },
                "git_commit": {
                    "type": "string",
                    "example": "abc123"
                },
                "version": {
                    "type": "string",
                    "example": "v1.0.0"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAccessToken": {
            "description": "Bearer {JWT access token}",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    },
    "tags": [
        {
            "description": "Authentication and authorization endpoints.",
            "name": "auth"
        },
        {
            "description": "Site adminstration tools",
            "name": "Site admin"
        },
        {
            "description": "Manage the Information Sharing Networks that are used to exchange signals between participating users.",
            "name": "ISN configuration"
        },
        {
            "description": "Grant accounts read or write access to an ISN",
            "name": "ISN Permissions"
        },
        {
            "description": "View information about the configured ISNs",
            "name": "ISN details"
        },
        {
            "description": "Define the format of the data being shared in an ISN",
            "name": "Signal types"
        },
        {
            "description": "Manage service account end points",
            "name": "Service accounts"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "Signals ISN API",
	Description:      "Signals ISN service API for managing Information Sharing Networks\n\n## Common Error Responses\nAll endpoints may return:\n- `400` Malformed request (invalid json, missing required fields, etc.)\n- `401` Unauthorized (invalid credentials)\n- `403` Forbidden (insufficient permissions)\n- `413` Request body exceeds size limit\n- `429` Rate limit exceeded\n- `500` Internal server error\n\nIndividual endpoints document their specific business logic errors.\n\n## Request Limits\nAll endpoints are protected by:\n- **Rate limiting**: Configurable requests per second (default: 100 RPS, 20 burst)\n- **Request size limits**: 64KB for admin/auth endpoints, 5MB for signal ingestion\n\nCheck the X-Max-Request-Body response header for the configured limit on signals payload.\n\nThe rate limit is set globaly and prevents abuse of the service.\nIn production there will be additional protections in place such as per-IP rate limiting provided by the load balancer/reverse proxy.\n\n## Authentication & Authorization\n\n### OAuth\nThis API serves as an OAuth 2.0 Authorization Server for multiple client applications. The server supports web users and service accounts.\n\n### Authentication Flows\n- **Web users**: Direct authentication via /auth/login → receive JWT access token + HTTP-only refresh cookie → use bearer tokens for API calls\n- **Service accounts**: Clients implement OAuth Client Credentials flow → receive JWT access token → use bearer tokens for API calls\n\n### Token Usage\nAll protected API endpoints require valid JWT access tokens in the Authorization header:\n```\nAuthorization: Bearer <jwt-access-token>\n```\n\n**Token Refresh (Web Users):**\n- Client calls `/oauth/token?grant_type=refresh_token` with both bearer token AND HTTP-only cookie\n- API validates both credentials and issues new access token + rotated refresh cookie\n- Client receives new bearer token for subsequent API calls\n\n**Token Refresh (Service Accounts):**\n- Client calls `/oauth/token?grant_type=client_credentials` with client ID/secret\n- API validates credentials and issues new access token\n- Client receives new bearer token for subsequent API calls\n\n**Token Lifetimes:**\n- Access tokens: 30 minutes\n- Refresh tokens: 30 days (web users only)\n\n### CSRF Protection\nThe /oauth API endpoints use a http-only cookie to exchange refresh tokens but also require a bearer token, preventing CSRF attacks.\n\n### CORS Protection\nBy default the server will start with ALLOWED_ORIGINS=*\n\nThis should not be used in production - you must specify the list of client origins that are allowed to access the API in the ALLOWED_ORIGINS environment variable before starting the server.\n\n## Date/Time Handling:\n\n**URL Parameters**: The following ISO 8601 formats are accepted in URL query parameters:\n- 2006-01-02T15:04:05Z (UTC)\n- 2006-01-02T15:04:05+07:00 (with offset)\n- 2006-01-02T15:04:05.999999999Z (nano precision)\n- 2006-01-02 (date only, treated as start of day UTC: 2006-01-02T00:00:00Z)\n\nNote: If the timestamp contains a timezone offset (as in +07:00), the + must be percent-encoded as %2B in the query.\n\n**Response Bodies**: All date/time fields in JSON responses use RFC3339 format (ISO 8601):\n- Example: \"2025-06-03T13:47:47.331787+01:00\"",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
