package ui

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

func (s *Server) handleHome(w http.ResponseWriter, r *http.Request) {
	// Check if user has valid token without refresh attempt
	accessTokenCookie, err := r.<PERSON>("access_token")
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	// Check token status
	parser := jwt.NewParser(jwt.WithoutClaimsValidation())
	claims := &jwt.RegisteredClaims{}

	_, _, err = parser.ParseUnverified(accessTokenCookie.Value, claims)
	if err != nil {
		s.logger.Error().Msgf("error: could not parse access token :%v", err)
		s.redirectToLogin(w, r)
		return
	}

	if claims.ExpiresAt == nil {
		s.logger.Error().Msg("error: claims expiry is not set")
		s.redirectToLogin(w, r)
		return
	}

	// token is not expired - Continue
	if claims.ExpiresAt.After(time.Now()) {
		http.Redirect(w, r, "/dashboard", http.StatusSeeOther)
		return
	}

	// expired
	s.redirectToLogin(w, r)

}

func (s *Server) handleLogin(w http.ResponseWriter, r *http.Request) {
	// Render login page
	component := LoginPage()
	if err := component.Render(r.Context(), w); err != nil {
		s.logger.Error().Err(err).Msg("Failed to render login page")
	}
}

func (s *Server) handleLoginPost(w http.ResponseWriter, r *http.Request) {
	email := r.FormValue("email")
	password := r.FormValue("password")

	if email == "" || password == "" {
		// Return error fragment for HTMX
		component := LoginError("Email and password are required")
		if err := component.Render(r.Context(), w); err != nil {
			s.logger.Error().Err(err).Msg("Failed to render login error")
		}
		return
	}
	// Authenticate with signalsd API
	loginResp, apiCookies, err := s.authService.AuthenticateUser(email, password)
	if err != nil {
		s.logger.Error().Err(err).Msg("Authentication failed")
		component := LoginError("Invalid email or password")
		if err := component.Render(r.Context(), w); err != nil {
			s.logger.Error().Err(err).Msg("Failed to render login error")
		}
		return
	}

	// Forward cookies from API response to browser
	for _, cookie := range apiCookies {
		http.SetCookie(w, cookie)
	}

	// Set access token cookie (the API automatically sets the refresh token cookie)
	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    loginResp.AccessToken,
		Path:     "/",
		HttpOnly: true,
		Secure:   s.config.Environment == "prod",
		MaxAge:   loginResp.ExpiresIn + 60, // JWT expiry + 1 minute buffer
	})

	// Store permissions data for dropdown population
	if len(loginResp.Perms) > 0 {
		permsJSON, err := json.Marshal(loginResp.Perms)
		if err == nil {
			// Base64 encode to avoid cookie encoding issues
			encodedPerms := base64.StdEncoding.EncodeToString(permsJSON)
			http.SetCookie(w, &http.Cookie{
				Name:     "user_perms",
				Value:    encodedPerms,
				Path:     "/",
				HttpOnly: true,
				Secure:   s.config.Environment == "prod",
				MaxAge:   loginResp.ExpiresIn + 60,
			})
		}
	}

	// Return success response for HTMX
	w.Header().Set("HX-Redirect", "/dashboard")
	w.WriteHeader(http.StatusOK)
}

func (s *Server) handleLogout(w http.ResponseWriter, r *http.Request) {
	// Clear access token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   s.config.Environment == "prod",
	})

	// Clear refresh token cookie (matches signalsd API cookie settings)
	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    "",
		Path:     "/", // Must match the path used when setting the cookie
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   s.config.Environment == "prod",
	})

	// Clear permissions cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "user_perms",
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   s.config.Environment == "prod",
	})

	// Redirect to login page
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/login")
		w.WriteHeader(http.StatusOK)
	} else {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
	}
}

func (s *Server) handleDashboard(w http.ResponseWriter, r *http.Request) {
	// Render dashboard page
	component := DashboardPage()
	if err := component.Render(r.Context(), w); err != nil {
		s.logger.Error().Err(err).Msg("Failed to render dashboard page")
	}
}

func (s *Server) handleSignalSearch(w http.ResponseWriter, r *http.Request) {
	// Get permissions data from cookie
	permsCookie, err := r.Cookie("user_perms")
	if err != nil {
		s.logger.Error().Err(err).Msg("No permissions cookie found")
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	// Decode base64 cookie value
	decodedPerms, err := base64.StdEncoding.DecodeString(permsCookie.Value)
	if err != nil {
		s.logger.Error().Err(err).Msgf("Failed to decode permissions cookie: %s", permsCookie.Value)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	var perms map[string]IsnPerms
	if err := json.Unmarshal(decodedPerms, &perms); err != nil {
		s.logger.Error().Err(err).Msgf("Failed to parse permissions JSON: %s", string(decodedPerms))
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	// Convert permissions to ISN list for dropdown
	isns := make([]ISN, 0, len(perms))
	for isnSlug := range perms {
		isns = append(isns, ISN{
			Slug:    isnSlug,
			Title:   isnSlug, // Use slug as title for now
			IsInUse: true,
		})
	}

	// Render search page
	component := SignalSearchPage(isns, perms, nil, "")
	if err := component.Render(r.Context(), w); err != nil {
		s.logger.Error().Err(err).Msg("Failed to render signal search page")
	}
}

func (s *Server) handleGetSignalTypes(w http.ResponseWriter, r *http.Request) {
	isnSlug := r.FormValue("isn_slug")
	if isnSlug == "" {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Get permissions data from cookie
	permsCookie, err := r.Cookie("user_perms")
	if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Decode base64 cookie value
	decodedPerms, err := base64.StdEncoding.DecodeString(permsCookie.Value)
	if err != nil {
		s.logger.Error().Err(err).Msgf("Failed to decode permissions cookie in signal types handler: %s", permsCookie.Value)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	var perms map[string]IsnPerms
	if err := json.Unmarshal(decodedPerms, &perms); err != nil {
		s.logger.Error().Err(err).Msgf("Failed to parse permissions JSON in signal types handler: %s", string(decodedPerms))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Get signal types for the selected ISN
	isnPerm, exists := perms[isnSlug]
	if !exists {
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Parse signal type paths to extract unique signal types
	signalTypeMap := make(map[string]bool)
	for _, path := range isnPerm.SignalTypePaths {
		// Path format: "signal-type-slug/v1.0.0"
		parts := strings.Split(path, "/v")
		if len(parts) == 2 {
			signalTypeMap[parts[0]] = true
		}
	}

	// Convert to slice
	signalTypes := make([]string, 0, len(signalTypeMap))
	for signalType := range signalTypeMap {
		signalTypes = append(signalTypes, signalType)
	}

	// Render signal types dropdown options
	component := SignalTypeOptionsFromStrings(signalTypes)
	if err := component.Render(r.Context(), w); err != nil {
		s.logger.Error().Err(err).Msg("Failed to render signal type options")
	}
}

func (s *Server) handleGetSignalVersions(w http.ResponseWriter, r *http.Request) {
	isnSlug := r.FormValue("isn_slug")
	signalTypeSlug := r.FormValue("signal_type_slug")
	if isnSlug == "" || signalTypeSlug == "" {
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	// Get permissions data from cookie
	permsCookie, err := r.Cookie("user_perms")
	if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		return
	}

	// Decode base64 cookie value
	decodedPerms, err := base64.StdEncoding.DecodeString(permsCookie.Value)
	if err != nil {
		s.logger.Error().Err(err).Msgf("Failed to decode permissions cookie in versions handler: %s", permsCookie.Value)
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	var perms map[string]IsnPerms
	if err := json.Unmarshal(decodedPerms, &perms); err != nil {
		s.logger.Error().Err(err).Msgf("Failed to parse permissions JSON in versions handler: %s", string(decodedPerms))
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Get signal types for the selected ISN
	isnPerm, exists := perms[isnSlug]
	if !exists {
		w.WriteHeader(http.StatusForbidden)
		return
	}

	// Find versions for the specific signal type
	versions := make([]string, 0)
	for _, path := range isnPerm.SignalTypePaths {
		// Path format: "signal-type-slug/v1.0.0"
		parts := strings.Split(path, "/v")
		if len(parts) == 2 && parts[0] == signalTypeSlug {
			versions = append(versions, parts[1])
		}
	}

	// Render version dropdown options
	component := VersionOptions(versions)
	if err := component.Render(r.Context(), w); err != nil {
		s.logger.Error().Err(err).Msg("Failed to render version options")
	}
}

func (s *Server) handleSearchSignals(w http.ResponseWriter, r *http.Request) {
	// Parse search parameters
	params := SignalSearchParams{
		ISNSlug:                 r.FormValue("isn_slug"),
		SignalTypeSlug:          r.FormValue("signal_type_slug"),
		SemVer:                  r.FormValue("sem_ver"),
		IsPublic:                r.FormValue("is_public") == "true",
		StartDate:               r.FormValue("start_date"),
		EndDate:                 r.FormValue("end_date"),
		AccountID:               r.FormValue("account_id"),
		SignalID:                r.FormValue("signal_id"),
		LocalRef:                r.FormValue("local_ref"),
		IncludeWithdrawn:        r.FormValue("include_withdrawn") == "true",
		IncludeCorrelated:       r.FormValue("include_correlated") == "true",
		IncludePreviousVersions: r.FormValue("include_previous_versions") == "true",
	}

	// Validate required parameters
	if params.ISNSlug == "" || params.SignalTypeSlug == "" || params.SemVer == "" {
		component := SearchError("ISN, Signal Type, and Version are required")
		if err := component.Render(r.Context(), w); err != nil {
			s.logger.Error().Err(err).Msg("Failed to render search error")
		}
		return
	}

	// Get access token for API calls
	var accessToken string
	if !params.IsPublic {
		accessTokenCookie, err := r.Cookie("access_token")
		if err != nil {
			component := SearchError("Authentication required for private ISN search")
			if err := component.Render(r.Context(), w); err != nil {
				s.logger.Error().Err(err).Msg("Failed to render search error")
			}
			return
		}
		accessToken = accessTokenCookie.Value
	}

	// Perform search
	searchResp, err := s.authService.SearchSignals(accessToken, params)
	if err != nil {
		s.logger.Error().Err(err).Msg("Signal search failed")
		component := SearchError(fmt.Sprintf("Search failed: %v", err))
		if err := component.Render(r.Context(), w); err != nil {
			s.logger.Error().Err(err).Msg("Failed to render search error")
		}
		return
	}

	// Render search results
	component := SearchResults(*searchResp)
	if err := component.Render(r.Context(), w); err != nil {
		s.logger.Error().Err(err).Msg("Failed to render search results")
	}
}

// Middleware - todo move

// RequireAuth is middleware that checks authentication and attempts token refresh if needed
func (s *Server) RequireAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		accessTokenCookie, err := r.Cookie("access_token")
		if err != nil {
			s.redirectToLogin(w, r)
			return
		}

		// Check token status
		parser := jwt.NewParser(jwt.WithoutClaimsValidation())
		claims := &jwt.RegisteredClaims{}

		_, _, err = parser.ParseUnverified(accessTokenCookie.Value, claims)
		if err != nil {
			s.logger.Error().Msgf("error: could not parse access token :%v", err)
			s.redirectToLogin(w, r)
			return
		}

		if claims.ExpiresAt == nil {
			s.logger.Error().Msg("error: claims expiry is not set")
			s.redirectToLogin(w, r)
			return
		}

		// token is not expired - Continue
		if claims.ExpiresAt.After(time.Now()) {
			next.ServeHTTP(w, r)
			return
		}

		// Access token has expired - attempt refresh
		// Get refresh token cookie to forward to API
		refreshTokenCookie, err := r.Cookie("refresh_token")
		if err != nil {
			s.redirectToLogin(w, r)
			return
		}

		// Attempt token refresh (forwarding refresh token cookie to API)
		loginResp, apiCookies, err := s.authService.RefreshToken(accessTokenCookie.Value, refreshTokenCookie)
		if err != nil {
			s.logger.Error().Err(err).Msg("Token refresh failed")
			s.redirectToLogin(w, r)
			return
		}

		// Forward cookies from API response (especially new refresh token cookie)
		for _, cookie := range apiCookies {
			http.SetCookie(w, cookie)
		}

		// Set new access token cookie
		http.SetCookie(w, &http.Cookie{
			Name:     "access_token",
			Value:    loginResp.AccessToken,
			Path:     "/",
			HttpOnly: true,
			Secure:   s.config.Environment == "prod",
			MaxAge:   loginResp.ExpiresIn + 60, // JWT expiry + 1 minute buffer for refresh logic
		})

		s.logger.Info().Msg("Access token refreshed successfully")
		next.ServeHTTP(w, r) // Continue with refreshed token
	})
}

// Helper method for redirecting to login
func (s *Server) redirectToLogin(w http.ResponseWriter, r *http.Request) {
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/login")
		w.WriteHeader(http.StatusOK)
	} else {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
	}
}
